namespace LeadTeams.Desktop.Services.BaseView
{
    public partial class SearchBox : LeadTeamsForm
    {
        protected List<ControlsViewModel> listControlView = new List<ControlsViewModel>();
        public List<ControlsValueModel> listControlValue = new List<ControlsValueModel>();

        private IDataSourceHandler _dataSourceHandler;

        public SearchBox(List<ControlsValueModel> listOfControls, IDataSourceHandler dataSourceHandler)
        {
            listControlValue = listOfControls;
            _dataSourceHandler = dataSourceHandler;
            InitializeComponent();

            btnSearch.Click += (s, e) => GetValues();
            this.Load += (s, e) =>
            {
                OpenForm.InitializeControls(this);
                if (listOfControls.Count > 0)
                    SetValuesFromList(listOfControls);
            };
        }

        protected List<ControlsViewModel> InitializeControls<T>(Type type)
        {
            List<ControlsViewModel> controlsViewModels = new List<ControlsViewModel>();

            PropertyDescriptor[] properties = TypeDescriptor.GetProperties(typeof(T)).Cast<PropertyDescriptor>().Where(prop => prop.Attributes[typeof(SearchableAttribute)] != null).ToArray();

            foreach (PropertyDescriptor prop in properties)
            {
                SearchableAttribute? attribute = prop.Attributes[typeof(SearchableAttribute)] as SearchableAttribute;
                string? propName = type.GetProperty(prop.Name)?.GetDisplayNameValue();

                LeadTeamsLabel lbl = new LeadTeamsLabel()
                {
                    Name = "lbl" + prop.Name,
                    Text = propName,
                    Anchor = AnchorStyles.Left | AnchorStyles.Right,
                    AutoSize = false,
                    TextAlign = System.Drawing.ContentAlignment.MiddleCenter,
                };
                if (attribute != null)
                {
                    switch (attribute.SelectedControls)
                    {
                        case (SearchableAttribute.SelectControls.TextBox):
                            LeadTeamsTextBox txt = new LeadTeamsTextBox()
                            {
                                Name = "txt" + prop.Name,
                                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                            };
                            controlsViewModels.Add(new ControlsViewModel { labelControl = lbl, valueControl = txt });
                            break;
                        case (SearchableAttribute.SelectControls.ComboBox):
                            var dataSource = _dataSourceHandler.SetDataSource(attribute.ModelName == "" ? prop.Name : attribute.ModelName);
                            if (dataSource is ServiceResult<IEnumerable<IdAndName>> serviceResult)
                                dataSource = serviceResult.Data;
                            var displayMember = _dataSourceHandler.SetDisplayMember(attribute.ModelName == "" ? prop.Name : attribute.ModelName);
                            var valueMember = _dataSourceHandler.SetValueMember(attribute.ModelName == "" ? prop.Name : attribute.ModelName);
                            LeadTeamsComboBox cmb = new LeadTeamsComboBox()
                            {
                                Name = "cmb" + prop.Name,
                                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                                DataSource = dataSource,
                                DisplayMember = displayMember,
                                ValueMember = valueMember,
                            };
                            controlsViewModels.Add(new ControlsViewModel { labelControl = lbl, valueControl = cmb });
                            break;
                        case (SearchableAttribute.SelectControls.CheckBox):
                            LeadTeamsCheckBox cbx = new LeadTeamsCheckBox()
                            {
                                Name = "cbx" + prop.Name,
                                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                            };
                            controlsViewModels.Add(new ControlsViewModel { labelControl = lbl, valueControl = cbx });
                            break;
                        case (SearchableAttribute.SelectControls.DateTimePicker):
                            LeadTeamsLabel lblFrom = new LeadTeamsLabel()
                            {
                                Name = "lbl" + prop.Name + "From",
                                Text = propName,
                                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                                AutoSize = false,
                                TextAlign = System.Drawing.ContentAlignment.MiddleCenter,
                            };
                            LeadTeamsLabel lblTo = new LeadTeamsLabel()
                            {
                                Name = "lbl" + prop.Name + "To",
                                Text = propName,
                                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                                AutoSize = false,
                                TextAlign = System.Drawing.ContentAlignment.MiddleCenter,
                            };

                            LeadTeamsDateTimePicker dtpFrom = new LeadTeamsDateTimePicker()
                            {
                                Name = "dtp" + prop.Name + "From",
                                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                                ValueDateTime = DateTime.Now.Date,
                                Format = DateTimePickerFormat.Short,
                            };
                            LeadTeamsDateTimePicker dtpTo = new LeadTeamsDateTimePicker()
                            {
                                Name = "dtp" + prop.Name + "To",
                                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                                ValueDateTime = DateTime.Now.Date,
                                Format = DateTimePickerFormat.Short,
                            };

                            controlsViewModels.Add(new ControlsViewModel { labelControl = lblFrom, valueControl = dtpFrom });
                            controlsViewModels.Add(new ControlsViewModel { labelControl = lblTo, valueControl = dtpTo });
                            break;
                    }
                }
            }

            return controlsViewModels;
        }

        protected void ViewControls()
        {
            int index = 0;
            tableLayoutPanel1.RowCount = 0;
            tableLayoutPanel1.RowStyles.Clear();
            listControlView.ForEach(x =>
            {
                tableLayoutPanel1.RowCount++;
                tableLayoutPanel1.RowStyles.Add(new RowStyle() { Height = 35, SizeType = SizeType.Absolute });
                {
                    tableLayoutPanel1.Controls.Add(x.labelControl, 0, index);
                    tableLayoutPanel1.Controls.Add(x.valueControl, 1, index);
                }
                index++;
            });
            tableLayoutPanel1.RowCount++;
            tableLayoutPanel1.SetRowSpan(btnSearch, tableLayoutPanel1.RowCount);
        }

        protected void GetValues()
        {
            listControlValue.Clear();
            listControlView.ForEach(x =>
            {
                listControlValue.Add(new ControlsValueModel()
                {
                    labelName = ValidateValue.GetCleanName(x.labelControl.Name),
                    labelText = x.labelControl.Text,
                    valueControl = GetControlValue(x.valueControl),
                });
            });

            this.DialogResult = DialogResult.OK;
        }

        public T GetValuesAsModel<T>()
        {
            T? t = (T?)Activator.CreateInstance(typeof(T));

            if (t == null)
                throw new TypeAccessException("Type not found");

            PropertyDescriptor[] properties = TypeDescriptor.GetProperties(typeof(T)).Cast<PropertyDescriptor>().Where(prop => prop.Attributes[typeof(SearchableAttribute)] != null).ToArray();

            foreach (PropertyDescriptor prop in properties)
            {
                var value = listControlValue.FirstOrDefault(x => x.labelName == prop.Name);
                if (value != null)
                {
                    if (prop.PropertyType == typeof(Ulid))
                        prop.SetValue(t, ValidateValue.ValidateUlid(value.valueControl));
                    else if (prop.PropertyType == typeof(Guid))
                        prop.SetValue(t, ValidateValue.ValidateGuid(value.valueControl));
                    else if (prop.PropertyType == typeof(int))
                        prop.SetValue(t, ValidateValue.ValidateInt(value.valueControl));
                    else if (prop.PropertyType == typeof(long))
                        prop.SetValue(t, ValidateValue.ValidateLong(value.valueControl));
                    else if (prop.PropertyType == typeof(short))
                        prop.SetValue(t, ValidateValue.ValidateShort(value.valueControl));
                    else if (prop.PropertyType == typeof(decimal))
                        prop.SetValue(t, ValidateValue.ValidateDecimal(value.valueControl));
                    else if (prop.PropertyType == typeof(double))
                        prop.SetValue(t, ValidateValue.ValidateDouble(value.valueControl));
                    else if (prop.PropertyType == typeof(bool))
                        prop.SetValue(t, ValidateValue.ValidateBool(value.valueControl));
                    else if (prop.PropertyType == typeof(DateTime))
                        prop.SetValue(t, ValidateValue.ValidateDateTime(value.valueControl));
                    else if (prop.PropertyType == typeof(string))
                        prop.SetValue(t, ValidateValue.ValidateString(value.valueControl));
                    else
                        prop.SetValue(t, value.valueControl);
                }
            }

            return t;
        }

        protected void SetValuesFromList(List<ControlsValueModel> ListOfControls)
        {
            bool found = false;

            ListOfControls.ForEach(loc =>
            {
                found = false;

                string loclabelControl = ValidateValue.GetCleanName(loc.labelName);
                var locvalueControl = loc.valueControl;
                listControlView.ForEach(lcv =>
                {
                    if (!found)
                    {
                        string lcvlabelControl = ValidateValue.GetCleanName(lcv.labelControl.Name);
                        var lcvvalueControl = lcv.valueControl;

                        if (loclabelControl == lcvlabelControl)
                        {
                            found = true;
                            if (!string.IsNullOrEmpty(locvalueControl?.ToString()))
                                SetControlValue(lcvvalueControl, locvalueControl);
                        }
                    }
                });
            });
        }

        public object? GetControlValue(Control control)
        {
            if (control is LeadTeamsTextBox textBox)
                return (textBox).Text;
            if (control is LeadTeamsComboBox comboBox)
                return (comboBox).SelectedValue;
            if (control is LeadTeamsCheckBox checkBox)
                return (checkBox).Checked;
            if (control is LeadTeamsDateTimePicker dateTimePicker)
                return (dateTimePicker).Value;

            return null;
        }

        public void SetControlValue(Control control, object value)
        {
            if (value != null)
            {
                if (control is LeadTeamsTextBox textBox)
                    textBox.Text = ValidateValue.ValidateString(value);
                if (control is LeadTeamsComboBox comboBox)
                    comboBox.SelectedValue = value;
                if (control is LeadTeamsCheckBox checkBox)
                    checkBox.Checked = (bool)value;
                if (control is LeadTeamsDateTimePicker dateTimePicker)
                    dateTimePicker.Value = ValidateValue.ValidateDateTime(value);
            }
        }
    }
}
