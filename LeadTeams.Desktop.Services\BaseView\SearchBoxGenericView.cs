namespace LeadTeams.Desktop.Services.BaseView
{
    public partial class SearchBoxGenericView<T> : SearchBox
    {
        public SearchBoxGenericView(T SearchModel, List<ControlsValueModel> listOfControls, IDataSourceHandler dataSourceHandler) :
            base(listOfControls, dataSourceHandler)
        {
            listControlView = InitializeControls<T>(typeof(T));
            ViewControls();
        }
    }
}
